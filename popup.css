* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #333;
}

.popup-container {
  width: 380px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px;
  text-align: center;
  position: relative;
}

.logo {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin-bottom: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.header h1 {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.content {
  padding: 24px;
}

.section {
  margin-bottom: 20px;
}

.label {
  display: block;
  margin-bottom: 8px;
}

.label span {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
  font-size: 14px;
}

.select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  font-size: 14px;
  color: #374151;
  transition: all 0.2s ease;
  cursor: pointer;
}

.select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 12px 0;
  transition: all 0.2s ease;
}

.checkbox-label:hover {
  background: #f9fafb;
  border-radius: 8px;
  padding-left: 8px;
}

.checkbox {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  background: #e5e7eb;
  border-radius: 4px;
  margin-right: 12px;
  position: relative;
  transition: all 0.2s ease;
}

.checkbox:checked + .checkmark {
  background: #667eea;
}

.checkbox:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 7px;
  top: 3px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkbox-label span:last-child {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.footer {
  padding: 20px 24px;
  background: #f9fafb;
  display: flex;
  gap: 12px;
}

.btn {
  flex: 1;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover {
  background: #5a67d8;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: #e5e7eb;
  color: #374151;
}

.btn-secondary:hover {
  background: #d1d5db;
  transform: translateY(-1px);
}

.info {
  padding: 20px 24px;
  background: #f8fafc;
  border-top: 1px solid #e5e7eb;
}

.shortcuts {
  margin-bottom: 16px;
}

.shortcuts h3 {
  font-size: 16px;
  color: #374151;
  margin-bottom: 12px;
  font-weight: 600;
}

.shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  font-size: 13px;
}

.key {
  background: #667eea;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  font-weight: 600;
}

.shortcut-item span:last-child {
  color: #6b7280;
}

.status {
  text-align: center;
  padding: 12px;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(10px);
}

.status.success {
  background: #d1fae5;
  color: #065f46;
  opacity: 1;
  transform: translateY(0);
}

.status.error {
  background: #fee2e2;
  color: #991b1b;
  opacity: 1;
  transform: translateY(0);
}

.status.info {
  background: #dbeafe;
  color: #1e40af;
  opacity: 1;
  transform: translateY(0);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.popup-container {
  animation: fadeIn 0.3s ease-out;
}

.section {
  animation: fadeIn 0.4s ease-out;
  animation-fill-mode: both;
}

.section:nth-child(1) { animation-delay: 0.1s; }
.section:nth-child(2) { animation-delay: 0.2s; }
.section:nth-child(3) { animation-delay: 0.3s; }
.section:nth-child(4) { animation-delay: 0.4s; }
.section:nth-child(5) { animation-delay: 0.5s; }

/* Service Status Styles */
.service-status {
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.service-status h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #374151;
}

.service-list {
  margin-bottom: 16px;
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e5e7eb;
}

.service-item:last-child {
  border-bottom: none;
}

.service-name {
  font-weight: 500;
  color: #374151;
  text-transform: capitalize;
}

.service-indicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 12px;
}

.service-indicator.enabled {
  background: #dcfce7;
  color: #166534;
}

.service-indicator.disabled {
  background: #fee2e2;
  color: #dc2626;
}

.service-indicator::before {
  content: '';
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

.btn.btn-small {
  padding: 6px 12px;
  font-size: 12px;
  margin-right: 8px;
}

.btn.btn-small:last-child {
  margin-right: 0;
}