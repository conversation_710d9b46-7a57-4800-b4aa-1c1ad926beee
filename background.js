class TranslationAPI {
  constructor() {
    this.cache = new Map();
    this.rateLimiter = new RateLimiter(10, 60000);
    this.retryCount = 3;
    this.retryDelay = 1000;
    
    // 多个翻译服务配置
    this.services = [
      {
        name: 'pollinations',
        baseURL: 'https://text.pollinations.ai/',
        model: 'openai-reasoning',
        enabled: true
      },
      {
        name: 'libre',
        baseURL: 'https://libretranslate.de/translate',
        enabled: true
      },
      {
        name: 'mymemory',
        baseURL: 'https://api.mymemory.translated.net/get',
        enabled: true
      }
    ];
    
    this.currentServiceIndex = 0;
  }

  async translate(text, targetLang = '中文', sourceLang = 'auto') {
    if (!text || text.length > 5000) {
      throw new Error('文本长度无效');
    }

    if (!this.rateLimiter.canMakeRequest()) {
      throw new Error('请求过于频繁，请稍后再试');
    }

    const cacheKey = `${text}_${targetLang}_${sourceLang}`;
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    // 尝试所有可用的翻译服务
    for (let serviceIndex = 0; serviceIndex < this.services.length; serviceIndex++) {
      const currentIndex = (this.currentServiceIndex + serviceIndex) % this.services.length;
      const service = this.services[currentIndex];
      
      if (!service.enabled) continue;

      try {
        let translation;
        
        switch (service.name) {
          case 'pollinations':
            translation = await this.translateWithPollinations(text, targetLang, service);
            break;
          case 'libre':
            translation = await this.translateWithLibre(text, targetLang, sourceLang, service);
            break;
          case 'mymemory':
            translation = await this.translateWithMyMemory(text, targetLang, sourceLang, service);
            break;
        }

        if (translation && translation.trim()) {
          const cleanedTranslation = this.cleanTranslation(translation.trim());
          this.cache.set(cacheKey, cleanedTranslation);
          
          if (this.cache.size > 500) {
            this.clearOldCache();
          }

          // 更新当前服务索引为成功的服务
          this.currentServiceIndex = currentIndex;
          return cleanedTranslation;
        }
      } catch (error) {
        console.error(`Translation failed with ${service.name}:`, error);
        
        // 如果是402错误，禁用该服务
        if (error.message.includes('HTTP 402')) {
          console.warn(`Service ${service.name} returned 402, disabling temporarily`);
          service.enabled = false;
          // 5分钟后重新启用
          setTimeout(() => {
            service.enabled = true;
            console.log(`Re-enabled service ${service.name}`);
          }, 300000);
        }
        
        // 如果是最后一个服务也失败了，抛出错误
        if (serviceIndex === this.services.length - 1) {
          throw new Error(`所有翻译服务都失败了: ${error.message}`);
        }
      }
    }
    
    throw new Error('没有可用的翻译服务');
  }

  async translateWithPollinations(text, targetLang, service) {
    const prompt = `请将以下文本翻译成${targetLang}，只返回翻译结果，不要添加任何解释或格式：\n\n${text}`;
    
    const encodedPrompt = encodeURIComponent(prompt);
    const url = `${service.baseURL}${encodedPrompt}`;
    
    const params = new URLSearchParams({
      model: service.model,
      seed: Math.floor(Math.random() * 1000)
    });

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 25000);

    const response = await fetch(`${url}?${params}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'ImmersiveTranslation/1.0'
      },
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.text();
  }

  async translateWithLibre(text, targetLang, sourceLang, service) {
    const langMap = {
      '中文': 'zh',
      '英语': 'en',
      '日语': 'ja',
      '韩语': 'ko',
      '法语': 'fr',
      '德语': 'de',
      '西班牙语': 'es',
      '俄语': 'ru',
      'auto': 'auto'
    };

    const target = langMap[targetLang] || 'zh';
    const source = langMap[sourceLang] || 'auto';

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000);

    const response = await fetch(service.baseURL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        q: text,
        source: source,
        target: target,
        format: 'text'
      }),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    return data.translatedText;
  }

  async translateWithMyMemory(text, targetLang, sourceLang, service) {
    const langMap = {
      '中文': 'zh-CN',
      '英语': 'en-US',
      '日语': 'ja-JP',
      '韩语': 'ko-KR',
      '法语': 'fr-FR',
      '德语': 'de-DE',
      '西班牙语': 'es-ES',
      '俄语': 'ru-RU'
    };

    const target = langMap[targetLang] || 'zh-CN';
    const source = langMap[sourceLang] || 'en-US';

    const params = new URLSearchParams({
      q: text,
      langpair: `${source}|${target}`
    });

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000);

    const response = await fetch(`${service.baseURL}?${params}`, {
      method: 'GET',
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.responseStatus === 200) {
      return data.responseData.translatedText;
    } else {
      throw new Error('MyMemory translation failed');
    }
  }

  cleanTranslation(text) {
    return text
      .replace(/^["']|["']$/g, '')
      .replace(/^\s*翻译[结果]?[:：]\s*/i, '')
      .replace(/^\s*译文[:：]\s*/i, '')
      .trim();
  }

  clearOldCache() {
    const entries = Array.from(this.cache.entries());
    this.cache.clear();
    entries.slice(-250).forEach(([key, value]) => {
      this.cache.set(key, value);
    });
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  getServiceStatus() {
    return this.services.map(service => ({
      name: service.name,
      enabled: service.enabled
    }));
  }

  enableAllServices() {
    this.services.forEach(service => {
      service.enabled = true;
    });
    console.log('All translation services re-enabled');
  }

  async detectLanguage(text) {
    if (!text || text.length > 1000) {
      return 'unknown';
    }

    // 使用第一个可用的服务进行语言检测
    const pollinationsService = this.services.find(s => s.name === 'pollinations' && s.enabled);
    
    if (!pollinationsService) {
      return 'unknown';
    }

    const prompt = `请检测以下文本的语言，只返回语言名称（如：英语、中文、日语等）：\n\n${text}`;
    
    try {
      const encodedPrompt = encodeURIComponent(prompt);
      const url = `${pollinationsService.baseURL}${encodedPrompt}`;
      
      const params = new URLSearchParams({
        model: pollinationsService.model
      });

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const response = await fetch(`${url}?${params}`, {
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);

      if (!response.ok) {
        return 'unknown';
      }

      const language = await response.text();
      return language.trim() || 'unknown';
    } catch (error) {
      console.error('Language detection error:', error);
      return 'unknown';
    }
  }
}

class RateLimiter {
  constructor(maxRequests, windowMs) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
    this.requests = [];
  }

  canMakeRequest() {
    const now = Date.now();
    this.requests = this.requests.filter(time => now - time < this.windowMs);
    
    if (this.requests.length < this.maxRequests) {
      this.requests.push(now);
      return true;
    }
    
    return false;
  }
}

const translator = new TranslationAPI();

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'translate') {
    translator.translate(request.text, request.targetLang, request.sourceLang)
      .then(translation => {
        sendResponse({ success: true, translation });
      })
      .catch(error => {
        sendResponse({ success: false, error: error.message });
      });
    return true;
  }

  if (request.action === 'detectLanguage') {
    translator.detectLanguage(request.text)
      .then(language => {
        sendResponse({ success: true, language });
      })
      .catch(error => {
        sendResponse({ success: false, error: error.message });
      });
    return true;
  }

  if (request.action === 'getSettings') {
    chrome.storage.sync.get(['translationSettings'], (result) => {
      const defaultSettings = {
        targetLanguage: '中文',
        translationMode: 'hover',
        autoTranslate: false,
        showOriginal: true,
        translationStyle: 'bubble'
      };
      sendResponse(result.translationSettings || defaultSettings);
    });
    return true;
  }

  if (request.action === 'saveSettings') {
    chrome.storage.sync.set({ translationSettings: request.settings }, () => {
      sendResponse({ success: true });
    });
    return true;
  }

  if (request.action === 'getServiceStatus') {
    sendResponse({ success: true, services: translator.getServiceStatus() });
    return true;
  }

  if (request.action === 'enableAllServices') {
    translator.enableAllServices();
    sendResponse({ success: true });
    return true;
  }
});

chrome.commands.onCommand.addListener((command) => {
  if (command === 'toggle-translation') {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      chrome.tabs.sendMessage(tabs[0].id, { action: 'toggleTranslation' });
    });
  }

  if (command === 'translate-selection') {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      chrome.tabs.sendMessage(tabs[0].id, { action: 'translateSelection' });
    });
  }
});

chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'translate-text' && info.selectionText) {
    chrome.tabs.sendMessage(tab.id, {
      action: 'translateText',
      text: info.selectionText
    });
  }
});

chrome.runtime.onInstalled.addListener(() => {
  chrome.contextMenus.create({
    id: 'translate-text',
    title: '翻译选中文本',
    contexts: ['selection']
  });
});