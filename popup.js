class PopupController {
  constructor() {
    this.elements = {};
    this.currentSettings = {};
    this.init();
  }

  init() {
    this.bindElements();
    this.loadSettings();
    this.loadServiceStatus();
    this.bindEvents();
  }

  bindElements() {
    this.elements = {
      targetLanguage: document.getElementById('targetLanguage'),
      translationMode: document.getElementById('translationMode'),
      translationStyle: document.getElementById('translationStyle'),
      showOriginal: document.getElementById('showOriginal'),
      autoTranslate: document.getElementById('autoTranslate'),
      saveSettings: document.getElementById('saveSettings'),
      resetSettings: document.getElementById('resetSettings'),
      status: document.getElementById('status'),
      serviceList: document.getElementById('serviceList'),
      refreshServices: document.getElementById('refreshServices'),
      enableAllServices: document.getElementById('enableAllServices')
    };
  }

  async loadSettings() {
    try {
      const settings = await this.getSettings();
      this.currentSettings = settings;
      this.populateForm(settings);
      this.showStatus('设置已加载', 'info');
    } catch (error) {
      this.showStatus('加载设置失败', 'error');
      console.error('Failed to load settings:', error);
    }
  }

  getSettings() {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage({ action: 'getSettings' }, (settings) => {
        resolve(settings);
      });
    });
  }

  populateForm(settings) {
    this.elements.targetLanguage.value = settings.targetLanguage || '中文';
    this.elements.translationMode.value = settings.translationMode || 'selection';
    this.elements.translationStyle.value = settings.translationStyle || 'bubble';
    this.elements.showOriginal.checked = settings.showOriginal !== false;
    this.elements.autoTranslate.checked = settings.autoTranslate || false;
  }

  bindEvents() {
    this.elements.saveSettings.addEventListener('click', () => {
      this.saveSettings();
    });

    this.elements.resetSettings.addEventListener('click', () => {
      this.resetSettings();
    });

    this.elements.refreshServices.addEventListener('click', () => {
      this.loadServiceStatus();
    });

    this.elements.enableAllServices.addEventListener('click', () => {
      this.enableAllServices();
    });

    Object.values(this.elements).forEach(element => {
      if (element.type === 'select-one' || element.type === 'checkbox') {
        element.addEventListener('change', () => {
          this.validateSettings();
        });
      }
    });

    document.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
        this.saveSettings();
      }
    });
  }

  validateSettings() {
    const isValid = this.elements.targetLanguage.value && 
                   this.elements.translationMode.value && 
                   this.elements.translationStyle.value;

    this.elements.saveSettings.disabled = !isValid;
    
    if (!isValid) {
      this.showStatus('请完整填写所有设置项', 'error');
    } else {
      this.hideStatus();
    }
  }

  async saveSettings() {
    try {
      const settings = this.collectFormData();
      
      const success = await this.persistSettings(settings);
      
      if (success) {
        this.currentSettings = settings;
        this.showStatus('设置保存成功！', 'success');
        
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
          if (tabs[0]) {
            chrome.tabs.sendMessage(tabs[0].id, { 
              action: 'settingsUpdated', 
              settings: settings 
            });
          }
        });
      } else {
        this.showStatus('保存设置失败', 'error');
      }
    } catch (error) {
      this.showStatus('保存设置时发生错误', 'error');
      console.error('Failed to save settings:', error);
    }
  }

  collectFormData() {
    return {
      targetLanguage: this.elements.targetLanguage.value,
      translationMode: this.elements.translationMode.value,
      translationStyle: this.elements.translationStyle.value,
      showOriginal: this.elements.showOriginal.checked,
      autoTranslate: this.elements.autoTranslate.checked,
      lastUpdated: Date.now()
    };
  }

  persistSettings(settings) {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage({ 
        action: 'saveSettings', 
        settings: settings 
      }, (response) => {
        resolve(response && response.success);
      });
    });
  }

  resetSettings() {
    const defaultSettings = {
      targetLanguage: '中文',
      translationMode: 'selection',
      translationStyle: 'bubble',
      showOriginal: true,
      autoTranslate: false
    };

    this.populateForm(defaultSettings);
    this.showStatus('设置已重置为默认值', 'info');
  }

  showStatus(message, type = 'info') {
    this.elements.status.textContent = message;
    this.elements.status.className = `status ${type}`;
    
    if (type === 'success') {
      setTimeout(() => this.hideStatus(), 3000);
    }
  }

  hideStatus() {
    this.elements.status.className = 'status';
  }

  showLanguageStats() {
    chrome.storage.local.get(['translationStats'], (result) => {
      const stats = result.translationStats || {};
      console.log('Translation statistics:', stats);
    });
  }

  async loadServiceStatus() {
    try {
      const response = await this.getServiceStatus();
      if (response.success) {
        this.displayServiceStatus(response.services);
      }
    } catch (error) {
      console.error('Failed to load service status:', error);
      this.showStatus('无法加载服务状态', 'error');
    }
  }

  getServiceStatus() {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage({ action: 'getServiceStatus' }, resolve);
    });
  }

  displayServiceStatus(services) {
    const serviceList = this.elements.serviceList;
    serviceList.innerHTML = '';

    const serviceNames = {
      'pollinations': 'Pollinations AI',
      'libre': 'LibreTranslate',
      'mymemory': 'MyMemory'
    };

    services.forEach(service => {
      const serviceItem = document.createElement('div');
      serviceItem.className = 'service-item';
      
      const serviceName = document.createElement('span');
      serviceName.className = 'service-name';
      serviceName.textContent = serviceNames[service.name] || service.name;
      
      const serviceIndicator = document.createElement('span');
      serviceIndicator.className = `service-indicator ${service.enabled ? 'enabled' : 'disabled'}`;
      serviceIndicator.textContent = service.enabled ? '可用' : '不可用';
      
      serviceItem.appendChild(serviceName);
      serviceItem.appendChild(serviceIndicator);
      serviceList.appendChild(serviceItem);
    });
  }

  async enableAllServices() {
    try {
      const response = await this.sendEnableAllServices();
      if (response.success) {
        this.showStatus('所有服务已启用', 'success');
        this.loadServiceStatus(); // 刷新状态显示
      } else {
        this.showStatus('启用服务失败', 'error');
      }
    } catch (error) {
      console.error('Failed to enable services:', error);
      this.showStatus('启用服务时发生错误', 'error');
    }
  }

  sendEnableAllServices() {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage({ action: 'enableAllServices' }, resolve);
    });
  }
}

class TranslationTester {
  constructor() {
    this.testButton = this.createTestButton();
  }

  createTestButton() {
    const button = document.createElement('button');
    button.textContent = '测试翻译';
    button.className = 'btn btn-secondary';
    button.style.marginTop = '12px';
    button.style.width = '100%';
    
    button.addEventListener('click', () => this.runTest());
    
    const footer = document.querySelector('.footer');
    footer.appendChild(button);
    
    return button;
  }

  async runTest() {
    const testText = 'Hello, this is a test translation.';
    
    this.testButton.textContent = '测试中...';
    this.testButton.disabled = true;

    try {
      const response = await new Promise((resolve) => {
        chrome.runtime.sendMessage({
          action: 'translate',
          text: testText,
          targetLang: '中文',
          sourceLang: 'auto'
        }, resolve);
      });

      if (response.success) {
        this.showTestResult('测试成功！', 'success');
        console.log('Test translation:', response.translation);
      } else {
        this.showTestResult('测试失败: ' + response.error, 'error');
      }
    } catch (error) {
      this.showTestResult('测试时发生错误', 'error');
      console.error('Test error:', error);
    } finally {
      this.testButton.textContent = '测试翻译';
      this.testButton.disabled = false;
    }
  }

  showTestResult(message, type) {
    const status = document.getElementById('status');
    status.textContent = message;
    status.className = `status ${type}`;
    
    setTimeout(() => {
      status.className = 'status';
    }, 3000);
  }
}

document.addEventListener('DOMContentLoaded', () => {
  new PopupController();
  new TranslationTester();
});